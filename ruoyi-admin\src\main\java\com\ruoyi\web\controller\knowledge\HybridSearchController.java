package com.ruoyi.web.controller.knowledge;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.knowledge.search.HybridSearchRequest;
import com.ruoyi.knowledge.search.HybridSearchResult;
import com.ruoyi.knowledge.service.IHybridSearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 混合检索控制器
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@RestController
@RequestMapping("/knowledge/hybrid-search")
public class HybridSearchController extends BaseController {

    @Autowired
    private IHybridSearchService hybridSearchService;

    /**
     * 执行混合检索
     */
    @PreAuthorize("@ss.hasPermi('knowledge:base:query')")
    @Log(title = "混合检索", businessType = BusinessType.OTHER)
    @PostMapping("/search")
    public AjaxResult hybridSearch(@RequestBody HybridSearchRequest request) {
        try {
            // 验证请求参数
            if (request.getKnowledgeBaseId() == null) {
                return error("知识库ID不能为空");
            }
            
            if (request.getQuery() == null || request.getQuery().trim().isEmpty()) {
                return error("查询内容不能为空");
            }

            // 执行混合检索
            HybridSearchResult result = hybridSearchService.hybridSearch(request);
            
            // 构建响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("results", result.getResults());
            data.put("vectorResultCount", result.getVectorResultCount());
            data.put("keywordResultCount", result.getKeywordResultCount());
            data.put("totalTime", result.getTotalTime());
            data.put("vectorSearchTime", result.getVectorSearchTime());
            data.put("keywordSearchTime", result.getKeywordSearchTime());
            data.put("fusionTime", result.getFusionTime());
            data.put("metadata", result.getMetadata());

            return success(data);

        } catch (Exception e) {
            logger.error("混合检索失败", e);
            return error("混合检索失败: " + e.getMessage());
        }
    }

    /**
     * 简化的混合检索接口
     */
    @PreAuthorize("@ss.hasPermi('knowledge:base:query')")
    @Log(title = "简化混合检索", businessType = BusinessType.OTHER)
    @GetMapping("/search/{knowledgeBaseId}")
    public AjaxResult simpleHybridSearch(
            @PathVariable Long knowledgeBaseId,
            @RequestParam String query,
            @RequestParam(defaultValue = "10") int maxResults) {
        try {
            HybridSearchResult result = hybridSearchService.hybridSearch(knowledgeBaseId, query, maxResults);
            
            // 构建简化的响应数据
            Map<String, Object> data = new HashMap<>();
            data.put("results", result.getResults());
            data.put("totalCount", result.getResults().size());
            data.put("totalTime", result.getTotalTime());

            return success(data);

        } catch (Exception e) {
            logger.error("简化混合检索失败", e);
            return error("混合检索失败: " + e.getMessage());
        }
    }

    /**
     * 获取关键词索引统计信息
     */
    @PreAuthorize("@ss.hasPermi('knowledge:base:query')")
    @GetMapping("/keyword-index/stats")
    public AjaxResult getKeywordIndexStats() {
        try {
            long documentCount = hybridSearchService.getKeywordIndexDocumentCount();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("documentCount", documentCount);
            stats.put("indexType", "In-Memory TF-IDF");
            stats.put("status", documentCount > 0 ? "active" : "empty");

            return success(stats);

        } catch (Exception e) {
            logger.error("获取关键词索引统计信息失败", e);
            return error("获取统计信息失败: " + e.getMessage());
        }
    }

    /**
     * 清空关键词索引
     */
    @PreAuthorize("@ss.hasPermi('knowledge:base:edit')")
    @Log(title = "清空关键词索引", businessType = BusinessType.DELETE)
    @DeleteMapping("/keyword-index/clear")
    public AjaxResult clearKeywordIndex() {
        try {
            hybridSearchService.clearKeywordIndex();
            return success("关键词索引已清空");

        } catch (Exception e) {
            logger.error("清空关键词索引失败", e);
            return error("清空关键词索引失败: " + e.getMessage());
        }
    }

    /**
     * 测试混合检索性能
     */
    @PreAuthorize("@ss.hasPermi('knowledge:base:query')")
    @Log(title = "混合检索性能测试", businessType = BusinessType.OTHER)
    @PostMapping("/performance-test")
    public AjaxResult performanceTest(@RequestBody Map<String, Object> testParams) {
        try {
            Long knowledgeBaseId = Long.valueOf(testParams.get("knowledgeBaseId").toString());
            String query = testParams.get("query").toString();
            int iterations = Integer.parseInt(testParams.getOrDefault("iterations", 10).toString());
            
            // 执行多次测试
            long totalTime = 0;
            int totalResults = 0;
            
            for (int i = 0; i < iterations; i++) {
                HybridSearchResult result = hybridSearchService.hybridSearch(knowledgeBaseId, query, 10);
                totalTime += result.getTotalTime();
                totalResults += result.getResults().size();
            }
            
            // 计算平均值
            Map<String, Object> performanceData = new HashMap<>();
            performanceData.put("iterations", iterations);
            performanceData.put("averageTime", totalTime / iterations);
            performanceData.put("averageResults", totalResults / iterations);
            performanceData.put("totalTime", totalTime);
            performanceData.put("query", query);
            performanceData.put("knowledgeBaseId", knowledgeBaseId);

            return success(performanceData);

        } catch (Exception e) {
            logger.error("混合检索性能测试失败", e);
            return error("性能测试失败: " + e.getMessage());
        }
    }
}
