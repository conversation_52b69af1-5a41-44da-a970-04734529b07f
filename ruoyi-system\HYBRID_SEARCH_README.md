# 混合检索功能说明

## 概述

本项目已成功实现了混合检索功能，结合了向量相似度搜索和关键词搜索，提供更准确和全面的知识库检索能力。

## 功能特性

### 1. 混合搜索策略
- **向量搜索**: 基于语义相似度的检索
- **关键词搜索**: 基于TF-IDF算法的精确匹配
- **结果融合**: 支持多种融合算法

### 2. 融合算法
- **加权融合**: 根据配置权重合并结果
- **RRF (Reciprocal Rank Fusion)**: 倒数排名融合
- **线性组合**: 简单的线性权重组合

### 3. 配置参数
```yaml
knowledge:
  hybrid:
    search:
      enabled: true
      vector:
        weight: 0.7
      keyword:
        weight: 0.3
```

## API 接口

### 1. 混合搜索接口
```http
POST /hybrid-search/search
Content-Type: application/json

{
  "query": "人工智能",
  "knowledgeBaseId": 1,
  "maxResults": 10,
  "vectorWeight": 0.7,
  "keywordWeight": 0.3,
  "rerankingAlgorithm": "WEIGHTED_FUSION"
}
```

### 2. 性能测试接口
```http
POST /hybrid-search/performance-test
Content-Type: application/json

{
  "query": "机器学习",
  "knowledgeBaseId": 1,
  "iterations": 100
}
```

## 使用示例

### Java 代码示例
```java
@Autowired
private KnowledgeRagService ragService;

// 使用混合搜索
List<String> results = ragService.searchInKnowledgeBase(
    1L,           // 知识库ID
    "人工智能",    // 查询文本
    10,           // 最大结果数
    true          // 启用混合搜索
);
```

### 直接使用混合搜索策略
```java
@Autowired
private HybridSearchStrategy hybridSearchStrategy;

HybridSearchRequest request = new HybridSearchRequest();
request.setQuery("机器学习");
request.setKnowledgeBaseId(1L);
request.setMaxResults(10);
request.setVectorWeight(0.7);
request.setKeywordWeight(0.3);

HybridSearchResult result = hybridSearchStrategy.search(request);
```

## 技术实现

### 1. 核心组件
- `HybridSearchStrategy`: 混合搜索策略接口
- `DefaultHybridSearchStrategy`: 默认实现
- `InMemoryKeywordSearchEngine`: 关键词搜索引擎
- `HybridSearchController`: REST API控制器

### 2. 关键词搜索引擎
- 使用TF-IDF算法计算文档相关性
- 支持中英文分词
- 内存索引，快速检索
- 支持文档增删改查

### 3. 结果融合
- 标准化分数处理
- 多种融合算法支持
- 可配置权重参数

## 测试验证

项目包含完整的单元测试：
- `HybridSearchTest`: 混合搜索功能测试
- 关键词搜索引擎测试
- 结果融合算法测试
- 配置参数验证测试

运行测试：
```bash
mvn test -Dtest=HybridSearchTest
```

## 性能优化

### 1. 索引优化
- 内存索引结构优化
- 倒排索引实现
- 文档频率缓存

### 2. 搜索优化
- 并行搜索执行
- 结果缓存机制
- 分页查询支持

### 3. 配置调优
- 向量权重: 0.7 (语义搜索为主)
- 关键词权重: 0.3 (精确匹配为辅)
- 最小分数阈值: 0.1

## 后续扩展

1. **分布式搜索**: 支持多节点部署
2. **实时索引**: 支持文档实时更新
3. **查询扩展**: 同义词和相关词扩展
4. **个性化排序**: 基于用户行为的个性化排序
5. **多语言支持**: 更好的中文分词和多语言处理

## 注意事项

1. 关键词搜索引擎目前使用内存存储，重启后需要重新构建索引
2. 大量文档时建议调整JVM内存参数
3. 生产环境建议使用外部搜索引擎（如Elasticsearch）
4. 定期清理无效索引以释放内存
