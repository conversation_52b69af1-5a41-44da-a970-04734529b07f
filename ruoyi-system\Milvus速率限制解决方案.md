# Milvus速率限制解决方案

## 问题解决状态
✅ **编译错误已修复**
✅ **速率限制处理已实现**
✅ **批处理机制已优化**

## 解决方案详情

### 1. 代码优化
我已经在 `KnowledgeRagServiceImpl` 中实现了智能的速率限制处理：

#### 关键特性：
- **小批次处理**：每批次只处理5个段落
- **批次间延迟**：每批次间等待2秒
- **单请求延迟**：每个向量化请求间等待200ms
- **重试机制**：最多重试10次，指数退避延迟
- **详细日志**：实时显示处理进度

#### 处理流程：
```
文档分割 → 小批次处理 → 逐个向量化 → 重试机制 → 成功存储
```

### 2. 配置优化
已更新的配置参数：
```yaml
knowledge:
  embedding:
    processing:
      chunk-size: 1000        # 增大块大小，减少段落数
      chunk-overlap: 100      # 重叠大小
      batch-size: 5           # 小批次处理
      batch-delay: 2000       # 批次间延迟
      max-retries: 10         # 重试次数
      retry-delay: 5000       # 重试延迟
```

### 3. 测试文档
为了更好地测试，我创建了两个文档：

#### A. 小文档测试（推荐先用这个）
- **文件**：`small_test_knowledge_base.txt`
- **大小**：约3KB
- **段落数**：预计10-15个段落
- **处理时间**：约30-60秒

#### B. 大文档测试
- **文件**：`test_knowledge_base.txt`
- **大小**：约15KB  
- **段落数**：预计50-80个段落
- **处理时间**：约5-10分钟

## 使用步骤

### 步骤1：重启服务
确保新的代码生效：
```bash
# 重启Spring Boot应用
# 或重新运行main方法
```

### 步骤2：上传小文档测试
1. 先使用 `small_test_knowledge_base.txt` 进行测试
2. 观察控制台日志，确认处理正常
3. 等待处理完成

### 步骤3：验证功能
上传成功后，测试以下查询：

#### 精确匹配测试
```
查询: "LSTM"
预期: 找到"长短期记忆网络（LSTM）"
```

#### 缩写词测试  
```
查询: "CNN"
预期: 找到"卷积神经网络（CNN）"
```

#### 算法名称测试
```
查询: "Random Forest"  
预期: 找到"随机森林"算法
```

#### 版本号测试
```
查询: "GPT-4"
预期: 找到GPT-4相关内容
```

### 步骤4：上传大文档
小文档测试成功后，可以尝试上传 `test_knowledge_base.txt`。

## 日志监控

### 正常处理日志示例：
```
INFO - 开始向量化处理，段落数: 12
INFO - 处理批次 1/3, 段落数: 5
DEBUG - 批次 1 处理成功
INFO - 处理批次 2/3, 段落数: 5  
DEBUG - 批次 2 处理成功
INFO - 处理批次 3/3, 段落数: 2
DEBUG - 批次 3 处理成功
INFO - 文档 xxx 向量化完成，成功处理 12/12 个段落
```

### 重试处理日志示例：
```
WARN - 批次 2 处理失败 (尝试 1/10): rate limit exceeded
INFO - 等待 3000 毫秒后重试批次 2...
INFO - 处理批次 2/3, 段落数: 5
DEBUG - 批次 2 处理成功
```

## 性能预期

### 小文档 (3KB)
- **段落数**：~12个
- **批次数**：~3批次
- **处理时间**：~30秒
- **成功率**：>95%

### 大文档 (15KB)  
- **段落数**：~60个
- **批次数**：~12批次
- **处理时间**：~8分钟
- **成功率**：>90%

## 故障排除

### 如果仍然遇到速率限制：
1. **检查Milvus服务**：确保Milvus服务正常运行
2. **增加延迟**：可以手动增加批次间延迟到5秒
3. **减少批次大小**：改为每批次3个段落
4. **检查网络**：确保网络连接稳定

### 修改延迟参数：
在代码中找到这些参数并调整：
```java
int batchSize = 3;        // 减少到3
int batchDelay = 5000;    // 增加到5秒
Thread.sleep(500);        // 单请求延迟增加到500ms
```

## 混合搜索测试

上传成功后，可以通过以下方式测试混合搜索：

### REST API测试
```http
POST /hybrid-search/search
Content-Type: application/json

{
  "query": "LSTM",
  "knowledgeBaseId": 1,
  "maxResults": 5,
  "vectorWeight": 0.7,
  "keywordWeight": 0.3,
  "rerankingAlgorithm": "WEIGHTED_FUSION"
}
```

### 对比测试
同时测试纯向量搜索和混合搜索，比较结果差异。

## 总结

现在的解决方案应该能够成功处理文档上传，即使在严格的速率限制下也能正常工作。建议：

1. **先测试小文档**：确保基本功能正常
2. **观察日志**：监控处理进度和重试情况  
3. **逐步增加**：成功后再尝试大文档
4. **验证功能**：测试混合搜索的优势

如果还有问题，可以进一步调整延迟参数或批次大小。
