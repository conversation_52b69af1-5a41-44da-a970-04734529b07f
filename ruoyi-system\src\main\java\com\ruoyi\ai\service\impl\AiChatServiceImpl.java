package com.ruoyi.ai.service.impl;

import com.ruoyi.ai.config.AiConfig;
import com.ruoyi.ai.domain.AiChatMessage;
import com.ruoyi.ai.domain.AiChatSession;
import com.ruoyi.ai.domain.dto.AiChatRequest;
import com.ruoyi.ai.domain.dto.AiChatResponse;
import com.ruoyi.ai.domain.dto.AiSessionRequest;
import com.ruoyi.ai.service.IAiChatMessageService;
import com.ruoyi.ai.service.IAiChatService;
import com.ruoyi.ai.service.IAiChatSessionService;
import com.ruoyi.knowledge.service.IKnowledgeRagService;
import dev.langchain4j.memory.ChatMemory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

/**
 * AI聊天综合服务实现
 * 
 * <AUTHOR>
 * @date 2024-07-09
 */
@Service
public class AiChatServiceImpl implements IAiChatService {

    private static final Logger logger = LoggerFactory.getLogger(AiChatServiceImpl.class);

    @Autowired
    private IAiChatSessionService sessionService;

    @Autowired
    private IAiChatMessageService messageService;

    @Autowired
    private AiConfig aiConfig;

    @Autowired
    private IKnowledgeRagService knowledgeRagService;

    /**
     * 创建新会话
     */
    @Override
    @Transactional
    public AiChatSession createSession(AiSessionRequest request, Long userId, String userName) {
        String sessionId = sessionService.createSession(
                request.getTitle(),
                request.getModel(),
                userId,
                userName,
                request.getRemark());

        return sessionService.selectAiChatSessionBySessionId(sessionId);
    }

    /**
     * 获取用户会话列表
     */
    @Override
    public List<AiChatSession> getUserSessions(Long userId) {
        return sessionService.selectAiChatSessionListByUserId(userId);
    }

    /**
     * 获取会话历史消息
     */
    @Override
    public List<AiChatMessage> getSessionHistory(String sessionId, Long userId) {
        // 检查权限
        if (!checkSessionPermission(sessionId, userId)) {
            throw new RuntimeException("无权限访问该会话");
        }

        return messageService.selectAiChatMessageListBySessionId(sessionId);
    }

    /**
     * 发送消息（同步）
     */
    @Override
    @Transactional
    public AiChatResponse sendMessage(AiChatRequest request, Long userId, String userName) {
        // 检查权限
        if (!checkSessionPermission(request.getSessionId(), userId)) {
            throw new RuntimeException("无权限访问该会话");
        }

        long startTime = System.currentTimeMillis();

        try {
            // 保存用户消息
            messageService.saveUserMessage(
                    request.getSessionId(),
                    request.getMessage(),
                    userId,
                    userName,
                    request.getModel());

            // 获取或创建会话记忆
            ChatMemory chatMemory = aiConfig.getOrCreateChatMemory(request.getSessionId());

            // 构建最终的消息内容（可能包含知识库搜索结果）
            String finalMessage = buildEnhancedMessage(request.getMessage(), request.getKnowledgeBaseId());

            // 创建AI助手并发送消息
            AiConfig.AiAssistant assistant = aiConfig.createAiAssistant(chatMemory);
            String aiResponse = assistant.chat(finalMessage);

            long responseTime = System.currentTimeMillis() - startTime;

            // 保存AI消息
            String messageId = messageService.saveAiMessage(
                    request.getSessionId(),
                    aiResponse,
                    request.getModel(),
                    responseTime,
                    null // Token数量暂时为null，可以后续扩展
            );

            // 更新会话活跃时间和消息数量
            sessionService.updateLastActiveTime(request.getSessionId());
            sessionService.increaseMessageCount(request.getSessionId(), 2); // 用户消息+AI消息

            return new AiChatResponse(messageId, aiResponse, responseTime);

        } catch (Exception e) {
            logger.error("AI聊天发生错误: {}", e.getMessage(), e);
            throw new RuntimeException("AI服务暂时不可用，请稍后重试");
        }
    }

    /**
     * 删除会话
     */
    @Override
    @Transactional
    public boolean deleteSession(String sessionId, Long userId) {
        // 检查权限
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            // 删除会话消息
            messageService.deleteAiChatMessageBySessionId(sessionId);

            // 删除会话
            int result = sessionService.deleteAiChatSessionBySessionId(sessionId);

            // 清除会话记忆
            aiConfig.clearChatMemory(sessionId);

            return result > 0;
        } catch (Exception e) {
            logger.error("删除会话发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 清空会话消息
     */
    @Override
    @Transactional
    public boolean clearSessionMessages(String sessionId, Long userId) {
        // 检查权限
        if (!checkSessionPermission(sessionId, userId)) {
            return false;
        }

        try {
            // 清空消息
            int result = messageService.clearAiChatMessageBySessionId(sessionId);

            // 重置消息数量
            AiChatSession session = sessionService.selectAiChatSessionBySessionId(sessionId);
            if (session != null) {
                session.setMessageCount(0L);
                sessionService.updateAiChatSession(session);
            }

            // 清除会话记忆
            aiConfig.clearChatMemory(sessionId);

            return result >= 0;
        } catch (Exception e) {
            logger.error("清空会话消息发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取AI模型列表
     */
    @Override
    public List<String> getAvailableModels() {
        return Arrays.asList("qwen-plus");
    }

    /**
     * 检查会话权限
     */
    @Override
    public boolean checkSessionPermission(String sessionId, Long userId) {
        return sessionService.checkSessionOwnership(sessionId, userId);
    }

    /**
     * 构建增强的消息内容（结合知识库搜索结果）
     */
    private String buildEnhancedMessage(String originalMessage, Long knowledgeBaseId) {
        logger.info("开始构建增强消息 - 原始消息: {}, 知识库ID: {}", originalMessage, knowledgeBaseId);

        // 如果没有指定知识库，直接返回原始消息
        if (knowledgeBaseId == null) {
            logger.info("未指定知识库，返回原始消息");
            return originalMessage;
        }

        try {
            // 检查知识库是否存在
            boolean exists = knowledgeRagService.knowledgeBaseExists(knowledgeBaseId);
            logger.info("知识库 {} 是否存在: {}", knowledgeBaseId, exists);
            if (!exists) {
                logger.warn("指定的知识库不存在: {}", knowledgeBaseId);
                return originalMessage;
            }

            // 获取知识库中的文档数量
            long documentCount = knowledgeRagService.getDocumentCount(knowledgeBaseId);
            logger.info("知识库 {} 中有 {} 个文档", knowledgeBaseId, documentCount);

            // 在知识库中搜索相关内容（使用混合检索）
            logger.info("开始在知识库 {} 中搜索内容，查询: {}", knowledgeBaseId, originalMessage);
            List<String> searchResults = knowledgeRagService.searchInKnowledgeBase(
                    knowledgeBaseId, originalMessage, 3, true); // 启用混合检索
            logger.info("混合检索完成，找到 {} 条结果", searchResults.size());

            // 打印搜索结果详情用于调试
            if (!searchResults.isEmpty()) {
                logger.info("搜索结果详情:");
                for (int i = 0; i < searchResults.size(); i++) {
                    String result = searchResults.get(i);
                    logger.info("结果 {}: {} (长度: {})", i + 1,
                            result.length() > 100 ? result.substring(0, 100) + "..." : result,
                            result.length());
                }
            }

            // 如果没有找到相关内容，返回原始消息
            if (searchResults.isEmpty()) {
                logger.warn("在知识库 {} 中未找到与问题相关的内容，查询: {}", knowledgeBaseId, originalMessage);
                return originalMessage;
            }

            // 构建包含知识库内容的增强消息
            StringBuilder enhancedMessage = new StringBuilder();
            enhancedMessage.append("请基于以下知识库内容回答用户问题：\n\n");
            enhancedMessage.append("【知识库内容】\n");

            for (int i = 0; i < searchResults.size(); i++) {
                enhancedMessage.append(String.format("%d. %s\n\n", i + 1, searchResults.get(i)));
            }

            enhancedMessage.append("【用户问题】\n");
            enhancedMessage.append(originalMessage);
            enhancedMessage.append("\n\n");
            enhancedMessage.append("请根据上述知识库内容回答用户问题。如果知识库内容无法回答用户问题，请说明并尽力提供其他帮助。");

            logger.info("为用户问题增强了 {} 条知识库内容，增强消息总长度: {}", searchResults.size(), enhancedMessage.length());
            logger.debug("完整增强消息: {}", enhancedMessage.toString());
            return enhancedMessage.toString();

        } catch (Exception e) {
            logger.error("构建增强消息时发生错误: {}", e.getMessage(), e);
            return originalMessage;
        }
    }
}
