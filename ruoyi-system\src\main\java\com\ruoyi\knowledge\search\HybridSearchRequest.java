package com.ruoyi.knowledge.search;

/**
 * 混合检索请求对象
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
public class HybridSearchRequest {

    /** 知识库ID */
    private Long knowledgeBaseId;

    /** 查询内容 */
    private String query;

    /** 最大结果数 */
    private int maxResults = 10;

    /** 向量检索权重 (0.0-1.0) */
    private double vectorWeight = 0.7;

    /** 关键词检索权重 (0.0-1.0) */
    private double keywordWeight = 0.3;

    /** 向量检索最小相似度分数 */
    private double minVectorScore = 0.6;

    /** 关键词检索最小相关性分数 */
    private double minKeywordScore = 0.1;

    /** 是否启用向量检索 */
    private boolean enableVectorSearch = true;

    /** 是否启用关键词检索 */
    private boolean enableKeywordSearch = true;

    /** 重排序算法类型 */
    private RerankingType rerankingType = RerankingType.WEIGHTED_FUSION;

    public HybridSearchRequest() {}

    public HybridSearchRequest(Long knowledgeBaseId, String query) {
        this.knowledgeBaseId = knowledgeBaseId;
        this.query = query;
    }

    public HybridSearchRequest(Long knowledgeBaseId, String query, int maxResults) {
        this.knowledgeBaseId = knowledgeBaseId;
        this.query = query;
        this.maxResults = maxResults;
    }

    // Getters and Setters
    public Long getKnowledgeBaseId() {
        return knowledgeBaseId;
    }

    public void setKnowledgeBaseId(Long knowledgeBaseId) {
        this.knowledgeBaseId = knowledgeBaseId;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public int getMaxResults() {
        return maxResults;
    }

    public void setMaxResults(int maxResults) {
        this.maxResults = maxResults;
    }

    public double getVectorWeight() {
        return vectorWeight;
    }

    public void setVectorWeight(double vectorWeight) {
        this.vectorWeight = vectorWeight;
    }

    public double getKeywordWeight() {
        return keywordWeight;
    }

    public void setKeywordWeight(double keywordWeight) {
        this.keywordWeight = keywordWeight;
    }

    public double getMinVectorScore() {
        return minVectorScore;
    }

    public void setMinVectorScore(double minVectorScore) {
        this.minVectorScore = minVectorScore;
    }

    public double getMinKeywordScore() {
        return minKeywordScore;
    }

    public void setMinKeywordScore(double minKeywordScore) {
        this.minKeywordScore = minKeywordScore;
    }

    public boolean isEnableVectorSearch() {
        return enableVectorSearch;
    }

    public void setEnableVectorSearch(boolean enableVectorSearch) {
        this.enableVectorSearch = enableVectorSearch;
    }

    public boolean isEnableKeywordSearch() {
        return enableKeywordSearch;
    }

    public void setEnableKeywordSearch(boolean enableKeywordSearch) {
        this.enableKeywordSearch = enableKeywordSearch;
    }

    public RerankingType getRerankingType() {
        return rerankingType;
    }

    public void setRerankingType(RerankingType rerankingType) {
        this.rerankingType = rerankingType;
    }

    /**
     * 重排序算法类型
     */
    public enum RerankingType {
        /** 加权融合 */
        WEIGHTED_FUSION,
        /** RRF (Reciprocal Rank Fusion) */
        RRF,
        /** 线性组合 */
        LINEAR_COMBINATION
    }
}
