package com.ruoyi.knowledge.search;

import java.util.List;
import java.util.Map;

/**
 * 混合检索结果对象
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
public class HybridSearchResult {

    /** 检索结果列表 */
    private List<SearchResultItem> results;

    /** 向量检索结果数量 */
    private int vectorResultCount;

    /** 关键词检索结果数量 */
    private int keywordResultCount;

    /** 总检索时间(毫秒) */
    private long totalTime;

    /** 向量检索时间(毫秒) */
    private long vectorSearchTime;

    /** 关键词检索时间(毫秒) */
    private long keywordSearchTime;

    /** 结果融合时间(毫秒) */
    private long fusionTime;

    /** 额外的元数据信息 */
    private Map<String, Object> metadata;

    public HybridSearchResult() {}

    public HybridSearchResult(List<SearchResultItem> results) {
        this.results = results;
    }

    // Getters and Setters
    public List<SearchResultItem> getResults() {
        return results;
    }

    public void setResults(List<SearchResultItem> results) {
        this.results = results;
    }

    public int getVectorResultCount() {
        return vectorResultCount;
    }

    public void setVectorResultCount(int vectorResultCount) {
        this.vectorResultCount = vectorResultCount;
    }

    public int getKeywordResultCount() {
        return keywordResultCount;
    }

    public void setKeywordResultCount(int keywordResultCount) {
        this.keywordResultCount = keywordResultCount;
    }

    public long getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(long totalTime) {
        this.totalTime = totalTime;
    }

    public long getVectorSearchTime() {
        return vectorSearchTime;
    }

    public void setVectorSearchTime(long vectorSearchTime) {
        this.vectorSearchTime = vectorSearchTime;
    }

    public long getKeywordSearchTime() {
        return keywordSearchTime;
    }

    public void setKeywordSearchTime(long keywordSearchTime) {
        this.keywordSearchTime = keywordSearchTime;
    }

    public long getFusionTime() {
        return fusionTime;
    }

    public void setFusionTime(long fusionTime) {
        this.fusionTime = fusionTime;
    }

    public Map<String, Object> getMetadata() {
        return metadata;
    }

    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }

    /**
     * 检索结果项
     */
    public static class SearchResultItem {
        /** 文档内容 */
        private String content;

        /** 综合评分 */
        private double score;

        /** 向量相似度分数 */
        private double vectorScore;

        /** 关键词相关性分数 */
        private double keywordScore;

        /** 文档元数据 */
        private Map<String, String> metadata;

        /** 结果来源 */
        private ResultSource source;

        public SearchResultItem() {}

        public SearchResultItem(String content, double score) {
            this.content = content;
            this.score = score;
        }

        // Getters and Setters
        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public double getScore() {
            return score;
        }

        public void setScore(double score) {
            this.score = score;
        }

        public double getVectorScore() {
            return vectorScore;
        }

        public void setVectorScore(double vectorScore) {
            this.vectorScore = vectorScore;
        }

        public double getKeywordScore() {
            return keywordScore;
        }

        public void setKeywordScore(double keywordScore) {
            this.keywordScore = keywordScore;
        }

        public Map<String, String> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, String> metadata) {
            this.metadata = metadata;
        }

        public ResultSource getSource() {
            return source;
        }

        public void setSource(ResultSource source) {
            this.source = source;
        }

        /**
         * 结果来源类型
         */
        public enum ResultSource {
            /** 仅向量检索 */
            VECTOR_ONLY,
            /** 仅关键词检索 */
            KEYWORD_ONLY,
            /** 混合检索 */
            HYBRID
        }
    }
}
