package com.ruoyi.knowledge.search;

import java.util.List;

/**
 * 混合检索策略接口
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
public interface HybridSearchStrategy {

    /**
     * 执行混合检索
     * 
     * @param request 检索请求
     * @return 检索结果
     */
    HybridSearchResult search(HybridSearchRequest request);

    /**
     * 获取策略名称
     * 
     * @return 策略名称
     */
    String getStrategyName();

    /**
     * 获取策略描述
     * 
     * @return 策略描述
     */
    String getStrategyDescription();
}
