package com.ruoyi.knowledge.search;

import java.util.List;
import java.util.Map;

/**
 * 关键词搜索引擎接口
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
public interface KeywordSearchEngine {

    /**
     * 添加文档到索引
     * 
     * @param documentId 文档ID
     * @param content 文档内容
     * @param metadata 文档元数据
     */
    void addDocument(String documentId, String content, Map<String, String> metadata);

    /**
     * 从索引中删除文档
     * 
     * @param documentId 文档ID
     */
    void removeDocument(String documentId);

    /**
     * 搜索文档
     * 
     * @param query 查询内容
     * @param maxResults 最大结果数
     * @param minScore 最小相关性分数
     * @return 搜索结果
     */
    List<KeywordSearchResult> search(String query, int maxResults, double minScore);

    /**
     * 在指定知识库中搜索
     * 
     * @param knowledgeBaseId 知识库ID
     * @param query 查询内容
     * @param maxResults 最大结果数
     * @param minScore 最小相关性分数
     * @return 搜索结果
     */
    List<KeywordSearchResult> searchInKnowledgeBase(Long knowledgeBaseId, String query, int maxResults, double minScore);

    /**
     * 清空索引
     */
    void clearIndex();

    /**
     * 获取索引中的文档数量
     * 
     * @return 文档数量
     */
    long getDocumentCount();

    /**
     * 关键词搜索结果
     */
    class KeywordSearchResult {
        private String documentId;
        private String content;
        private double score;
        private Map<String, String> metadata;
        private List<String> highlightedFragments;

        public KeywordSearchResult() {}

        public KeywordSearchResult(String documentId, String content, double score) {
            this.documentId = documentId;
            this.content = content;
            this.score = score;
        }

        // Getters and Setters
        public String getDocumentId() {
            return documentId;
        }

        public void setDocumentId(String documentId) {
            this.documentId = documentId;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }

        public double getScore() {
            return score;
        }

        public void setScore(double score) {
            this.score = score;
        }

        public Map<String, String> getMetadata() {
            return metadata;
        }

        public void setMetadata(Map<String, String> metadata) {
            this.metadata = metadata;
        }

        public List<String> getHighlightedFragments() {
            return highlightedFragments;
        }

        public void setHighlightedFragments(List<String> highlightedFragments) {
            this.highlightedFragments = highlightedFragments;
        }
    }
}
