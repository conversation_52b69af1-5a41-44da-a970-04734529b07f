package com.ruoyi.knowledge.search.impl;

import com.ruoyi.knowledge.search.*;
import com.ruoyi.knowledge.store.CustomMilvusEmbeddingStore;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.EmbeddingStore;
import dev.langchain4j.store.embedding.EmbeddingSearchRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 默认混合检索策略实现
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@Component
public class DefaultHybridSearchStrategy implements HybridSearchStrategy {

    private static final Logger logger = LoggerFactory.getLogger(DefaultHybridSearchStrategy.class);

    @Autowired
    private EmbeddingStore<TextSegment> embeddingStore;

    @Autowired
    private EmbeddingModel embeddingModel;

    @Autowired
    private KeywordSearchEngine keywordSearchEngine;

    @Autowired(required = false)
    private CustomMilvusEmbeddingStore customMilvusEmbeddingStore;

    @Value("${knowledge.embedding.store.type:memory}")
    private String storeType;

    @Override
    public HybridSearchResult search(HybridSearchRequest request) {
        long startTime = System.currentTimeMillis();
        
        logger.info("开始混合检索: 知识库ID={}, 查询={}, 向量权重={}, 关键词权重={}", 
                request.getKnowledgeBaseId(), request.getQuery(), 
                request.getVectorWeight(), request.getKeywordWeight());

        HybridSearchResult result = new HybridSearchResult();
        List<HybridSearchResult.SearchResultItem> finalResults = new ArrayList<>();

        try {
            // 1. 向量检索
            List<VectorSearchItem> vectorResults = new ArrayList<>();
            long vectorStartTime = System.currentTimeMillis();
            
            if (request.isEnableVectorSearch()) {
                vectorResults = performVectorSearch(request);
                result.setVectorResultCount(vectorResults.size());
            }
            
            long vectorEndTime = System.currentTimeMillis();
            result.setVectorSearchTime(vectorEndTime - vectorStartTime);

            // 2. 关键词检索
            List<KeywordSearchEngine.KeywordSearchResult> keywordResults = new ArrayList<>();
            long keywordStartTime = System.currentTimeMillis();
            
            if (request.isEnableKeywordSearch()) {
                keywordResults = performKeywordSearch(request);
                result.setKeywordResultCount(keywordResults.size());
            }
            
            long keywordEndTime = System.currentTimeMillis();
            result.setKeywordSearchTime(keywordEndTime - keywordStartTime);

            // 3. 结果融合
            long fusionStartTime = System.currentTimeMillis();
            finalResults = fuseResults(vectorResults, keywordResults, request);
            long fusionEndTime = System.currentTimeMillis();
            result.setFusionTime(fusionEndTime - fusionStartTime);

            result.setResults(finalResults);
            result.setTotalTime(System.currentTimeMillis() - startTime);

            logger.info("混合检索完成: 向量结果={}, 关键词结果={}, 最终结果={}, 总耗时={}ms", 
                    vectorResults.size(), keywordResults.size(), finalResults.size(), result.getTotalTime());

        } catch (Exception e) {
            logger.error("混合检索失败: {}", e.getMessage(), e);
            result.setResults(new ArrayList<>());
            result.setTotalTime(System.currentTimeMillis() - startTime);
        }

        return result;
    }

    @Override
    public String getStrategyName() {
        return "默认混合检索策略";
    }

    @Override
    public String getStrategyDescription() {
        return "结合向量相似度检索和关键词检索，支持多种融合算法";
    }

    /**
     * 执行向量检索
     */
    private List<VectorSearchItem> performVectorSearch(HybridSearchRequest request) {
        try {
            logger.debug("执行向量检索...");

            Embedding queryEmbedding = embeddingModel.embed(request.getQuery()).content();
            List<EmbeddingMatch<TextSegment>> matches;

            // 根据存储类型选择不同的搜索策略
            if ("milvus".equalsIgnoreCase(storeType) && customMilvusEmbeddingStore != null) {
                // 使用Milvus时，直接在指定知识库的集合中搜索
                matches = customMilvusEmbeddingStore.searchInKnowledgeBase(request.getKnowledgeBaseId(),
                        EmbeddingSearchRequest.builder()
                                .queryEmbedding(queryEmbedding)
                                .maxResults(request.getMaxResults() * 2)
                                .minScore(request.getMinVectorScore())
                                .build())
                        .matches();
            } else {
                // 使用内存存储或Redis时，在全局存储中搜索然后过滤
                matches = embeddingStore.search(
                        EmbeddingSearchRequest.builder()
                                .queryEmbedding(queryEmbedding)
                                .maxResults(request.getMaxResults() * 3) // 增加搜索数量以便过滤
                                .minScore(request.getMinVectorScore())
                                .build()
                ).matches();
            }

            List<VectorSearchItem> results;
            if ("milvus".equalsIgnoreCase(storeType) && customMilvusEmbeddingStore != null) {
                // Milvus已经在特定集合中搜索，无需过滤
                results = matches.stream()
                        .map(match -> new VectorSearchItem(
                                match.embedded().text(),
                                match.score(),
                                convertMetadataToStringMap(match.embedded().metadata().toMap())
                        ))
                        .collect(Collectors.toList());
            } else {
                // 过滤指定知识库的结果
                results = matches.stream()
                        .filter(match -> {
                            String kbId = match.embedded().metadata().getString("knowledgeBaseId");
                            return request.getKnowledgeBaseId().toString().equals(kbId);
                        })
                        .map(match -> new VectorSearchItem(
                                match.embedded().text(),
                                match.score(),
                                convertMetadataToStringMap(match.embedded().metadata().toMap())
                        ))
                        .collect(Collectors.toList());
            }

            return results;

        } catch (Exception e) {
            logger.error("向量检索失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 执行关键词检索
     */
    private List<KeywordSearchEngine.KeywordSearchResult> performKeywordSearch(HybridSearchRequest request) {
        try {
            logger.debug("执行关键词检索...");
            
            return keywordSearchEngine.searchInKnowledgeBase(
                    request.getKnowledgeBaseId(),
                    request.getQuery(),
                    request.getMaxResults() * 2, // 获取更多结果用于融合
                    request.getMinKeywordScore()
            );

        } catch (Exception e) {
            logger.error("关键词检索失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 融合检索结果
     */
    private List<HybridSearchResult.SearchResultItem> fuseResults(
            List<VectorSearchItem> vectorResults,
            List<KeywordSearchEngine.KeywordSearchResult> keywordResults,
            HybridSearchRequest request) {

        logger.debug("开始融合检索结果: 向量结果={}, 关键词结果={}", vectorResults.size(), keywordResults.size());

        switch (request.getRerankingType()) {
            case RRF:
                return fuseWithRRF(vectorResults, keywordResults, request);
            case LINEAR_COMBINATION:
                return fuseWithLinearCombination(vectorResults, keywordResults, request);
            case WEIGHTED_FUSION:
            default:
                return fuseWithWeightedFusion(vectorResults, keywordResults, request);
        }
    }

    /**
     * 加权融合算法
     */
    private List<HybridSearchResult.SearchResultItem> fuseWithWeightedFusion(
            List<VectorSearchItem> vectorResults,
            List<KeywordSearchEngine.KeywordSearchResult> keywordResults,
            HybridSearchRequest request) {

        Map<String, HybridSearchResult.SearchResultItem> resultMap = new HashMap<>();

        // 处理向量检索结果
        for (VectorSearchItem item : vectorResults) {
            String content = item.getContent();
            HybridSearchResult.SearchResultItem resultItem = resultMap.computeIfAbsent(content, 
                    k -> new HybridSearchResult.SearchResultItem(content, 0.0));
            
            resultItem.setVectorScore(item.getScore());
            resultItem.setScore(resultItem.getScore() + item.getScore() * request.getVectorWeight());
            resultItem.setMetadata(item.getMetadata());
            
            if (resultItem.getSource() == null) {
                resultItem.setSource(HybridSearchResult.SearchResultItem.ResultSource.VECTOR_ONLY);
            } else if (resultItem.getSource() == HybridSearchResult.SearchResultItem.ResultSource.KEYWORD_ONLY) {
                resultItem.setSource(HybridSearchResult.SearchResultItem.ResultSource.HYBRID);
            }
        }

        // 处理关键词检索结果
        for (KeywordSearchEngine.KeywordSearchResult item : keywordResults) {
            String content = item.getContent();
            HybridSearchResult.SearchResultItem resultItem = resultMap.computeIfAbsent(content, 
                    k -> new HybridSearchResult.SearchResultItem(content, 0.0));
            
            resultItem.setKeywordScore(item.getScore());
            resultItem.setScore(resultItem.getScore() + item.getScore() * request.getKeywordWeight());
            
            if (resultItem.getMetadata() == null) {
                resultItem.setMetadata(item.getMetadata());
            }
            
            if (resultItem.getSource() == null) {
                resultItem.setSource(HybridSearchResult.SearchResultItem.ResultSource.KEYWORD_ONLY);
            } else if (resultItem.getSource() == HybridSearchResult.SearchResultItem.ResultSource.VECTOR_ONLY) {
                resultItem.setSource(HybridSearchResult.SearchResultItem.ResultSource.HYBRID);
            }
        }

        // 按分数排序并返回
        return resultMap.values().stream()
                .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
                .limit(request.getMaxResults())
                .collect(Collectors.toList());
    }

    /**
     * RRF (Reciprocal Rank Fusion) 算法
     */
    private List<HybridSearchResult.SearchResultItem> fuseWithRRF(
            List<VectorSearchItem> vectorResults,
            List<KeywordSearchEngine.KeywordSearchResult> keywordResults,
            HybridSearchRequest request) {

        final int k = 60; // RRF参数
        Map<String, Double> rrfScores = new HashMap<>();
        Map<String, HybridSearchResult.SearchResultItem> resultMap = new HashMap<>();

        // 处理向量检索结果
        for (int i = 0; i < vectorResults.size(); i++) {
            VectorSearchItem item = vectorResults.get(i);
            String content = item.getContent();
            double rrfScore = 1.0 / (k + i + 1);
            rrfScores.put(content, rrfScores.getOrDefault(content, 0.0) + rrfScore);
            
            HybridSearchResult.SearchResultItem resultItem = new HybridSearchResult.SearchResultItem(content, 0.0);
            resultItem.setVectorScore(item.getScore());
            resultItem.setMetadata(item.getMetadata());
            resultItem.setSource(HybridSearchResult.SearchResultItem.ResultSource.VECTOR_ONLY);
            resultMap.put(content, resultItem);
        }

        // 处理关键词检索结果
        for (int i = 0; i < keywordResults.size(); i++) {
            KeywordSearchEngine.KeywordSearchResult item = keywordResults.get(i);
            String content = item.getContent();
            double rrfScore = 1.0 / (k + i + 1);
            rrfScores.put(content, rrfScores.getOrDefault(content, 0.0) + rrfScore);
            
            HybridSearchResult.SearchResultItem resultItem = resultMap.get(content);
            if (resultItem == null) {
                resultItem = new HybridSearchResult.SearchResultItem(content, 0.0);
                resultItem.setMetadata(item.getMetadata());
                resultItem.setSource(HybridSearchResult.SearchResultItem.ResultSource.KEYWORD_ONLY);
                resultMap.put(content, resultItem);
            } else {
                resultItem.setSource(HybridSearchResult.SearchResultItem.ResultSource.HYBRID);
            }
            resultItem.setKeywordScore(item.getScore());
        }

        // 设置RRF分数并排序
        for (Map.Entry<String, Double> entry : rrfScores.entrySet()) {
            HybridSearchResult.SearchResultItem item = resultMap.get(entry.getKey());
            if (item != null) {
                item.setScore(entry.getValue());
            }
        }

        return resultMap.values().stream()
                .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
                .limit(request.getMaxResults())
                .collect(Collectors.toList());
    }

    /**
     * 线性组合算法
     */
    private List<HybridSearchResult.SearchResultItem> fuseWithLinearCombination(
            List<VectorSearchItem> vectorResults,
            List<KeywordSearchEngine.KeywordSearchResult> keywordResults,
            HybridSearchRequest request) {

        // 归一化分数
        List<VectorSearchItem> normalizedVectorResults = normalizeVectorScores(vectorResults);
        List<KeywordSearchEngine.KeywordSearchResult> normalizedKeywordResults = normalizeKeywordScores(keywordResults);

        return fuseWithWeightedFusion(normalizedVectorResults, normalizedKeywordResults, request);
    }

    /**
     * 归一化向量分数
     */
    private List<VectorSearchItem> normalizeVectorScores(List<VectorSearchItem> results) {
        if (results.isEmpty()) return results;

        double maxScore = results.stream().mapToDouble(VectorSearchItem::getScore).max().orElse(1.0);
        double minScore = results.stream().mapToDouble(VectorSearchItem::getScore).min().orElse(0.0);
        double range = maxScore - minScore;

        if (range == 0) return results;

        return results.stream()
                .map(item -> new VectorSearchItem(
                        item.getContent(),
                        (item.getScore() - minScore) / range,
                        item.getMetadata()
                ))
                .collect(Collectors.toList());
    }

    /**
     * 归一化关键词分数
     */
    private List<KeywordSearchEngine.KeywordSearchResult> normalizeKeywordScores(
            List<KeywordSearchEngine.KeywordSearchResult> results) {
        if (results.isEmpty()) return results;

        double maxScore = results.stream().mapToDouble(KeywordSearchEngine.KeywordSearchResult::getScore).max().orElse(1.0);
        double minScore = results.stream().mapToDouble(KeywordSearchEngine.KeywordSearchResult::getScore).min().orElse(0.0);
        double range = maxScore - minScore;

        if (range == 0) return results;

        return results.stream()
                .map(item -> {
                    KeywordSearchEngine.KeywordSearchResult normalized = new KeywordSearchEngine.KeywordSearchResult(
                            item.getDocumentId(),
                            item.getContent(),
                            (item.getScore() - minScore) / range
                    );
                    normalized.setMetadata(item.getMetadata());
                    normalized.setHighlightedFragments(item.getHighlightedFragments());
                    return normalized;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换元数据为字符串映射
     */
    private Map<String, String> convertMetadataToStringMap(Map<String, Object> objectMap) {
        Map<String, String> stringMap = new HashMap<>();
        if (objectMap != null) {
            for (Map.Entry<String, Object> entry : objectMap.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                stringMap.put(key, value != null ? value.toString() : null);
            }
        }
        return stringMap;
    }

    /**
     * 向量搜索结果项
     */
    private static class VectorSearchItem {
        private final String content;
        private final double score;
        private final Map<String, String> metadata;

        public VectorSearchItem(String content, double score, Map<String, String> metadata) {
            this.content = content;
            this.score = score;
            this.metadata = metadata;
        }

        public String getContent() {
            return content;
        }

        public double getScore() {
            return score;
        }

        public Map<String, String> getMetadata() {
            return metadata;
        }
    }
}
