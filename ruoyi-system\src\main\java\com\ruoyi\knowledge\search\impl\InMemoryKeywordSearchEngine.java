package com.ruoyi.knowledge.search.impl;

import com.ruoyi.knowledge.search.KeywordSearchEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 基于内存的关键词搜索引擎实现
 * 使用TF-IDF算法计算相关性分数
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@Component
public class InMemoryKeywordSearchEngine implements KeywordSearchEngine {

    private static final Logger logger = LoggerFactory.getLogger(InMemoryKeywordSearchEngine.class);

    /** 文档存储 */
    private final Map<String, DocumentInfo> documents = new ConcurrentHashMap<>();

    /** 倒排索引：词 -> 包含该词的文档ID列表 */
    private final Map<String, Set<String>> invertedIndex = new ConcurrentHashMap<>();

    /** 词频统计：文档ID -> 词 -> 词频 */
    private final Map<String, Map<String, Integer>> termFrequency = new ConcurrentHashMap<>();

    /** 文档频率：词 -> 包含该词的文档数量 */
    private final Map<String, Integer> documentFrequency = new ConcurrentHashMap<>();

    /** 分词模式 */
    private static final Pattern WORD_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5a-zA-Z0-9]+");

    @Override
    public void addDocument(String documentId, String content, Map<String, String> metadata) {
        try {
            logger.debug("添加文档到关键词索引: {}", documentId);

            // 如果文档已存在，先删除
            if (documents.containsKey(documentId)) {
                removeDocument(documentId);
            }

            // 存储文档信息
            DocumentInfo docInfo = new DocumentInfo(documentId, content, metadata);
            documents.put(documentId, docInfo);

            // 分词并建立索引
            List<String> terms = tokenize(content);
            Map<String, Integer> termFreq = new HashMap<>();

            // 计算词频
            for (String term : terms) {
                termFreq.put(term, termFreq.getOrDefault(term, 0) + 1);
            }

            // 更新倒排索引和文档频率
            for (String term : termFreq.keySet()) {
                // 更新倒排索引
                invertedIndex.computeIfAbsent(term, k -> ConcurrentHashMap.newKeySet()).add(documentId);
                
                // 更新文档频率
                documentFrequency.put(term, documentFrequency.getOrDefault(term, 0) + 1);
            }

            // 存储词频信息
            termFrequency.put(documentId, termFreq);

            logger.debug("文档 {} 索引完成，提取 {} 个唯一词汇", documentId, termFreq.size());

        } catch (Exception e) {
            logger.error("添加文档到关键词索引失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public void removeDocument(String documentId) {
        try {
            DocumentInfo docInfo = documents.get(documentId);
            if (docInfo == null) {
                return;
            }

            // 从倒排索引中删除
            Map<String, Integer> termFreq = termFrequency.get(documentId);
            if (termFreq != null) {
                for (String term : termFreq.keySet()) {
                    Set<String> docIds = invertedIndex.get(term);
                    if (docIds != null) {
                        docIds.remove(documentId);
                        if (docIds.isEmpty()) {
                            invertedIndex.remove(term);
                            documentFrequency.remove(term);
                        } else {
                            documentFrequency.put(term, documentFrequency.get(term) - 1);
                        }
                    }
                }
            }

            // 删除文档信息
            documents.remove(documentId);
            termFrequency.remove(documentId);

            logger.debug("从关键词索引中删除文档: {}", documentId);

        } catch (Exception e) {
            logger.error("从关键词索引中删除文档失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public List<KeywordSearchResult> search(String query, int maxResults, double minScore) {
        try {
            logger.debug("执行关键词搜索: {}, maxResults: {}, minScore: {}", query, maxResults, minScore);

            List<String> queryTerms = tokenize(query);
            if (queryTerms.isEmpty()) {
                return new ArrayList<>();
            }

            // 计算每个文档的相关性分数
            Map<String, Double> docScores = new HashMap<>();
            int totalDocs = documents.size();

            for (String docId : documents.keySet()) {
                double score = calculateTfIdfScore(docId, queryTerms, totalDocs);
                if (score >= minScore) {
                    docScores.put(docId, score);
                }
            }

            // 按分数排序并返回结果
            List<KeywordSearchResult> results = docScores.entrySet().stream()
                    .sorted(Map.Entry.<String, Double>comparingByValue().reversed())
                    .limit(maxResults)
                    .map(entry -> {
                        DocumentInfo docInfo = documents.get(entry.getKey());
                        KeywordSearchResult result = new KeywordSearchResult(
                                entry.getKey(), docInfo.getContent(), entry.getValue());
                        result.setMetadata(docInfo.getMetadata());
                        result.setHighlightedFragments(generateHighlights(docInfo.getContent(), queryTerms));
                        return result;
                    })
                    .collect(Collectors.toList());

            logger.debug("关键词搜索完成，找到 {} 个结果", results.size());
            return results;

        } catch (Exception e) {
            logger.error("关键词搜索失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<KeywordSearchResult> searchInKnowledgeBase(Long knowledgeBaseId, String query, int maxResults, double minScore) {
        // 先执行全局搜索，然后过滤指定知识库的结果
        List<KeywordSearchResult> allResults = search(query, maxResults * 3, minScore);
        
        return allResults.stream()
                .filter(result -> {
                    Map<String, String> metadata = result.getMetadata();
                    if (metadata != null) {
                        String kbId = metadata.get("knowledgeBaseId");
                        return knowledgeBaseId.toString().equals(kbId);
                    }
                    return false;
                })
                .limit(maxResults)
                .collect(Collectors.toList());
    }

    @Override
    public void clearIndex() {
        documents.clear();
        invertedIndex.clear();
        termFrequency.clear();
        documentFrequency.clear();
        logger.info("关键词索引已清空");
    }

    @Override
    public long getDocumentCount() {
        return documents.size();
    }

    /**
     * 分词方法
     */
    private List<String> tokenize(String text) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<String> tokens = new ArrayList<>();
        java.util.regex.Matcher matcher = WORD_PATTERN.matcher(text.toLowerCase());

        while (matcher.find()) {
            String token = matcher.group();
            // 对于中文，单字符也是有意义的词汇
            if (token.length() >= 1) {
                tokens.add(token);
            }
        }

        return tokens;
    }

    /**
     * 计算TF-IDF分数
     */
    private double calculateTfIdfScore(String docId, List<String> queryTerms, int totalDocs) {
        Map<String, Integer> docTermFreq = termFrequency.get(docId);
        if (docTermFreq == null) {
            return 0.0;
        }

        double score = 0.0;
        int docLength = docTermFreq.values().stream().mapToInt(Integer::intValue).sum();

        for (String term : queryTerms) {
            int tf = docTermFreq.getOrDefault(term, 0);
            if (tf > 0) {
                int df = documentFrequency.getOrDefault(term, 0);
                if (df > 0) {
                    // TF-IDF计算
                    double tfScore = (double) tf / docLength;
                    double idfScore = Math.log((double) totalDocs / df);
                    score += tfScore * idfScore;
                }
            }
        }

        return score;
    }

    /**
     * 生成高亮片段
     */
    private List<String> generateHighlights(String content, List<String> queryTerms) {
        List<String> highlights = new ArrayList<>();
        
        for (String term : queryTerms) {
            int index = content.toLowerCase().indexOf(term.toLowerCase());
            if (index >= 0) {
                int start = Math.max(0, index - 50);
                int end = Math.min(content.length(), index + term.length() + 50);
                String fragment = content.substring(start, end);
                
                // 简单的高亮标记
                fragment = fragment.replaceAll("(?i)" + Pattern.quote(term), "<mark>" + term + "</mark>");
                highlights.add(fragment);
            }
        }
        
        return highlights;
    }

    /**
     * 文档信息类
     */
    private static class DocumentInfo {
        private final String id;
        private final String content;
        private final Map<String, String> metadata;

        public DocumentInfo(String id, String content, Map<String, String> metadata) {
            this.id = id;
            this.content = content;
            this.metadata = metadata != null ? new HashMap<>(metadata) : new HashMap<>();
        }

        public String getId() {
            return id;
        }

        public String getContent() {
            return content;
        }

        public Map<String, String> getMetadata() {
            return metadata;
        }
    }
}
