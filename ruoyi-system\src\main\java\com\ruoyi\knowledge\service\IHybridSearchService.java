package com.ruoyi.knowledge.service;

import com.ruoyi.knowledge.search.HybridSearchRequest;
import com.ruoyi.knowledge.search.HybridSearchResult;

/**
 * 混合检索服务接口
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
public interface IHybridSearchService {

    /**
     * 执行混合检索
     * 
     * @param request 检索请求
     * @return 检索结果
     */
    HybridSearchResult hybridSearch(HybridSearchRequest request);

    /**
     * 简化的混合检索方法
     * 
     * @param knowledgeBaseId 知识库ID
     * @param query 查询内容
     * @param maxResults 最大结果数
     * @return 检索结果
     */
    HybridSearchResult hybridSearch(Long knowledgeBaseId, String query, int maxResults);

    /**
     * 添加文档到关键词索引
     * 
     * @param knowledgeBaseId 知识库ID
     * @param documentId 文档ID
     * @param content 文档内容
     */
    void addDocumentToKeywordIndex(Long knowledgeBaseId, Long documentId, String content);

    /**
     * 从关键词索引中删除文档
     * 
     * @param documentId 文档ID
     */
    void removeDocumentFromKeywordIndex(Long documentId);

    /**
     * 清空关键词索引
     */
    void clearKeywordIndex();

    /**
     * 获取关键词索引中的文档数量
     * 
     * @return 文档数量
     */
    long getKeywordIndexDocumentCount();
}
