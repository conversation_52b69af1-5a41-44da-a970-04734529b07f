package com.ruoyi.knowledge.service;

import dev.langchain4j.data.document.Document;
import dev.langchain4j.data.document.DocumentSplitter;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.data.segment.TextSegment;
import dev.langchain4j.model.embedding.EmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 带速率限制的文档处理服务
 * 用于处理向量化过程中的速率限制问题
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@Service
public class RateLimitedDocumentProcessor {

    private static final Logger logger = LoggerFactory.getLogger(RateLimitedDocumentProcessor.class);

    @Autowired
    private DocumentSplitter documentSplitter;

    @Autowired
    private EmbeddingModel embeddingModel;

    @Autowired
    private EmbeddingStore<TextSegment> embeddingStore;

    @Value("${knowledge.embedding.processing.batch-size:5}")
    private int batchSize;

    @Value("${knowledge.embedding.processing.batch-delay:2000}")
    private long batchDelay;

    @Value("${knowledge.embedding.processing.max-retries:10}")
    private int maxRetries;

    @Value("${knowledge.embedding.processing.retry-delay:5000}")
    private long retryDelay;

    @Value("${knowledge.embedding.processing.backoff-multiplier:1.5}")
    private double backoffMultiplier;

    /**
     * 处理文档并添加到向量存储
     * 
     * @param document 要处理的文档
     * @param knowledgeBaseId 知识库ID
     * @return 处理成功的段落数量
     */
    public int processDocument(Document document, Long knowledgeBaseId) {
        logger.info("开始处理文档: {}, 知识库ID: {}", document.metadata().getString("fileName"), knowledgeBaseId);

        // 分割文档
        List<TextSegment> segments = documentSplitter.split(document);
        logger.info("文档分割完成，共 {} 个段落", segments.size());

        // 为每个段落添加知识库ID元数据
        for (TextSegment segment : segments) {
            segment.metadata().put("knowledgeBaseId", knowledgeBaseId.toString());
        }

        // 分批处理段落
        int totalProcessed = 0;
        int batchCount = (segments.size() + batchSize - 1) / batchSize;

        for (int i = 0; i < batchCount; i++) {
            int startIndex = i * batchSize;
            int endIndex = Math.min(startIndex + batchSize, segments.size());
            List<TextSegment> batch = segments.subList(startIndex, endIndex);

            logger.info("处理批次 {}/{}, 段落数: {}", i + 1, batchCount, batch.size());

            int processed = processBatchWithRetry(batch);
            totalProcessed += processed;

            // 批次间延迟，避免速率限制
            if (i < batchCount - 1) {
                try {
                    Thread.sleep(batchDelay);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    logger.warn("批次延迟被中断");
                    break;
                }
            }
        }

        logger.info("文档处理完成，总共处理 {} 个段落", totalProcessed);
        return totalProcessed;
    }

    /**
     * 带重试机制的批次处理
     * 
     * @param batch 要处理的段落批次
     * @return 处理成功的段落数量
     */
    private int processBatchWithRetry(List<TextSegment> batch) {
        int attempt = 0;
        long currentRetryDelay = retryDelay;

        while (attempt < maxRetries) {
            try {
                // 向量化处理
                List<Embedding> embeddings = new ArrayList<>();
                for (TextSegment segment : batch) {
                    Embedding embedding = embeddingModel.embed(segment).content();
                    embeddings.add(embedding);

                    // 单个向量化请求间的小延迟
                    Thread.sleep(100);
                }

                // 存储到向量数据库
                embeddingStore.addAll(embeddings, batch);
                
                logger.debug("批次处理成功，段落数: {}", batch.size());
                return batch.size();

            } catch (Exception e) {
                attempt++;
                logger.warn("批次处理失败 (尝试 {}/{}): {}", attempt, maxRetries, e.getMessage());

                if (attempt >= maxRetries) {
                    logger.error("批次处理最终失败，已达到最大重试次数", e);
                    return 0;
                }

                // 指数退避延迟
                try {
                    logger.info("等待 {} 毫秒后重试...", currentRetryDelay);
                    Thread.sleep(currentRetryDelay);
                    currentRetryDelay = (long) (currentRetryDelay * backoffMultiplier);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    logger.warn("重试延迟被中断");
                    return 0;
                }
            }
        }

        return 0;
    }

    /**
     * 处理单个段落（用于测试）
     * 
     * @param segment 文本段落
     * @return 是否处理成功
     */
    public boolean processSingleSegment(TextSegment segment) {
        return processBatchWithRetry(Arrays.asList(segment)) > 0;
    }

    /**
     * 获取当前配置信息
     * 
     * @return 配置信息字符串
     */
    public String getConfigInfo() {
        return String.format(
            "批处理配置: 批次大小=%d, 批次延迟=%dms, 最大重试=%d, 重试延迟=%dms, 退避倍数=%.1f",
            batchSize, batchDelay, maxRetries, retryDelay, backoffMultiplier
        );
    }

    /**
     * 估算处理时间
     * 
     * @param segmentCount 段落数量
     * @return 估算的处理时间（秒）
     */
    public long estimateProcessingTime(int segmentCount) {
        int batchCount = (segmentCount + batchSize - 1) / batchSize;
        long totalDelay = (batchCount - 1) * batchDelay; // 批次间延迟
        long embeddingTime = segmentCount * 100; // 每个向量化100ms
        return (totalDelay + embeddingTime) / 1000;
    }
}
