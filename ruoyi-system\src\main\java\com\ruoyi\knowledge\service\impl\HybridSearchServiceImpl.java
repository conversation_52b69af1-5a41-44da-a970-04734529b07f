package com.ruoyi.knowledge.service.impl;

import com.ruoyi.knowledge.search.*;
import com.ruoyi.knowledge.service.IHybridSearchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 混合检索服务实现
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
@Service
public class HybridSearchServiceImpl implements IHybridSearchService {

    private static final Logger logger = LoggerFactory.getLogger(HybridSearchServiceImpl.class);

    @Autowired
    private HybridSearchStrategy hybridSearchStrategy;

    @Autowired
    private KeywordSearchEngine keywordSearchEngine;

    @Override
    public HybridSearchResult hybridSearch(HybridSearchRequest request) {
        try {
            logger.info("执行混合检索: 知识库ID={}, 查询={}", request.getKnowledgeBaseId(), request.getQuery());
            
            // 验证请求参数
            if (request.getKnowledgeBaseId() == null) {
                throw new IllegalArgumentException("知识库ID不能为空");
            }
            
            if (request.getQuery() == null || request.getQuery().trim().isEmpty()) {
                throw new IllegalArgumentException("查询内容不能为空");
            }

            // 执行混合检索
            HybridSearchResult result = hybridSearchStrategy.search(request);
            
            logger.info("混合检索完成: 知识库ID={}, 结果数量={}, 耗时={}ms", 
                    request.getKnowledgeBaseId(), 
                    result.getResults().size(), 
                    result.getTotalTime());
            
            return result;

        } catch (Exception e) {
            logger.error("混合检索失败: {}", e.getMessage(), e);
            
            // 返回空结果
            HybridSearchResult emptyResult = new HybridSearchResult();
            emptyResult.setResults(new java.util.ArrayList<>());
            emptyResult.setTotalTime(0);
            return emptyResult;
        }
    }

    @Override
    public HybridSearchResult hybridSearch(Long knowledgeBaseId, String query, int maxResults) {
        HybridSearchRequest request = new HybridSearchRequest(knowledgeBaseId, query, maxResults);
        return hybridSearch(request);
    }

    @Override
    public void addDocumentToKeywordIndex(Long knowledgeBaseId, Long documentId, String content) {
        try {
            logger.debug("添加文档到关键词索引: 知识库ID={}, 文档ID={}", knowledgeBaseId, documentId);
            
            // 构建文档元数据
            Map<String, String> metadata = new HashMap<>();
            metadata.put("knowledgeBaseId", knowledgeBaseId.toString());
            metadata.put("documentId", documentId.toString());
            
            // 生成唯一的文档ID
            String uniqueDocId = knowledgeBaseId + "_" + documentId;
            
            // 添加到关键词索引
            keywordSearchEngine.addDocument(uniqueDocId, content, metadata);
            
            logger.debug("文档已添加到关键词索引: {}", uniqueDocId);

        } catch (Exception e) {
            logger.error("添加文档到关键词索引失败: 知识库ID={}, 文档ID={}, 错误={}", 
                    knowledgeBaseId, documentId, e.getMessage(), e);
        }
    }

    @Override
    public void removeDocumentFromKeywordIndex(Long documentId) {
        try {
            logger.debug("从关键词索引中删除文档: 文档ID={}", documentId);
            
            // 注意：这里需要遍历所有可能的知识库ID来删除文档
            // 在实际实现中，可能需要维护一个文档ID到唯一ID的映射
            // 这里简化处理，假设有一个通用的删除方法
            keywordSearchEngine.removeDocument(documentId.toString());
            
            logger.debug("文档已从关键词索引中删除: {}", documentId);

        } catch (Exception e) {
            logger.error("从关键词索引中删除文档失败: 文档ID={}, 错误={}", documentId, e.getMessage(), e);
        }
    }

    @Override
    public void clearKeywordIndex() {
        try {
            logger.info("清空关键词索引");
            keywordSearchEngine.clearIndex();
            logger.info("关键词索引已清空");

        } catch (Exception e) {
            logger.error("清空关键词索引失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public long getKeywordIndexDocumentCount() {
        try {
            long count = keywordSearchEngine.getDocumentCount();
            logger.debug("关键词索引文档数量: {}", count);
            return count;

        } catch (Exception e) {
            logger.error("获取关键词索引文档数量失败: {}", e.getMessage(), e);
            return 0;
        }
    }
}
