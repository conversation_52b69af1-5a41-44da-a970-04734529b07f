package com.ruoyi.knowledge.search;

import com.ruoyi.knowledge.search.impl.InMemoryKeywordSearchEngine;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 混合检索功能测试
 * 
 * <AUTHOR>
 * @date 2024-12-27
 */
public class HybridSearchTest {

    private static final Logger logger = LoggerFactory.getLogger(HybridSearchTest.class);

    private InMemoryKeywordSearchEngine keywordSearchEngine;

    @BeforeEach
    void setUp() {
        keywordSearchEngine = new InMemoryKeywordSearchEngine();
    }

    @Test
    void testKeywordSearchEngine() {
        logger.info("测试关键词搜索引擎");

        // 添加测试文档
        Map<String, String> metadata1 = new HashMap<>();
        metadata1.put("knowledgeBaseId", "1");
        metadata1.put("documentId", "101");
        keywordSearchEngine.addDocument("1_101", "artificial intelligence is a branch of computer science", metadata1);

        Map<String, String> metadata2 = new HashMap<>();
        metadata2.put("knowledgeBaseId", "1");
        metadata2.put("documentId", "102");
        keywordSearchEngine.addDocument("1_102", "machine learning is a subset of artificial intelligence", metadata2);

        Map<String, String> metadata3 = new HashMap<>();
        metadata3.put("knowledgeBaseId", "2");
        metadata3.put("documentId", "201");
        keywordSearchEngine.addDocument("2_201", "deep learning is a branch of machine learning", metadata3);

        // 测试全局搜索
        List<KeywordSearchEngine.KeywordSearchResult> results = keywordSearchEngine.search("artificial", 10, 0.0);
        logger.info("全局搜索 'artificial' 找到 {} 个结果", results.size());

        // 打印调试信息
        for (KeywordSearchEngine.KeywordSearchResult result : results) {
            logger.info("搜索结果: 文档ID={}, 分数={}, 内容={}",
                    result.getDocumentId(), result.getScore(), result.getContent());
        }

        assertTrue(results.size() >= 1, "应该找到至少1个结果");

        // 测试知识库内搜索
        List<KeywordSearchEngine.KeywordSearchResult> kbResults = keywordSearchEngine.searchInKnowledgeBase(1L, "machine", 10, 0.0);
        logger.info("知识库1中搜索 'machine' 找到 {} 个结果", kbResults.size());
        assertTrue(kbResults.size() >= 1, "应该在知识库1中找到至少1个结果");

        // 验证结果内容
        for (KeywordSearchEngine.KeywordSearchResult result : kbResults) {
            logger.info("结果: 文档ID={}, 分数={}, 内容={}",
                    result.getDocumentId(), result.getScore(),
                    result.getContent().substring(0, Math.min(50, result.getContent().length())));
            assertTrue(result.getScore() >= 0, "分数应该大于等于0");
            assertNotNull(result.getContent(), "内容不应该为空");
        }

        // 测试文档数量
        long docCount = keywordSearchEngine.getDocumentCount();
        logger.info("索引中的文档数量: {}", docCount);
        assertEquals(3, docCount, "应该有3个文档");
    }

    @Test
    void testHybridSearchRequest() {
        logger.info("测试混合检索请求对象");

        HybridSearchRequest request = new HybridSearchRequest(1L, "测试查询", 10);
        
        // 验证默认值
        assertEquals(0.7, request.getVectorWeight(), 0.01, "默认向量权重应该是0.7");
        assertEquals(0.3, request.getKeywordWeight(), 0.01, "默认关键词权重应该是0.3");
        assertEquals(0.6, request.getMinVectorScore(), 0.01, "默认最小向量分数应该是0.6");
        assertEquals(0.1, request.getMinKeywordScore(), 0.01, "默认最小关键词分数应该是0.1");
        assertTrue(request.isEnableVectorSearch(), "默认应该启用向量检索");
        assertTrue(request.isEnableKeywordSearch(), "默认应该启用关键词检索");
        assertEquals(HybridSearchRequest.RerankingType.WEIGHTED_FUSION, request.getRerankingType(), "默认重排序类型应该是加权融合");

        // 测试参数设置
        request.setVectorWeight(0.8);
        request.setKeywordWeight(0.2);
        request.setMinVectorScore(0.7);
        request.setMinKeywordScore(0.2);
        request.setEnableVectorSearch(false);
        request.setEnableKeywordSearch(true);
        request.setRerankingType(HybridSearchRequest.RerankingType.RRF);

        assertEquals(0.8, request.getVectorWeight(), 0.01);
        assertEquals(0.2, request.getKeywordWeight(), 0.01);
        assertEquals(0.7, request.getMinVectorScore(), 0.01);
        assertEquals(0.2, request.getMinKeywordScore(), 0.01);
        assertFalse(request.isEnableVectorSearch());
        assertTrue(request.isEnableKeywordSearch());
        assertEquals(HybridSearchRequest.RerankingType.RRF, request.getRerankingType());
    }

    @Test
    void testHybridSearchResult() {
        logger.info("测试混合检索结果对象");

        HybridSearchResult result = new HybridSearchResult();
        
        // 创建测试结果项
        HybridSearchResult.SearchResultItem item1 = new HybridSearchResult.SearchResultItem("测试内容1", 0.8);
        item1.setVectorScore(0.7);
        item1.setKeywordScore(0.9);
        item1.setSource(HybridSearchResult.SearchResultItem.ResultSource.HYBRID);

        HybridSearchResult.SearchResultItem item2 = new HybridSearchResult.SearchResultItem("测试内容2", 0.6);
        item2.setVectorScore(0.6);
        item2.setKeywordScore(0.0);
        item2.setSource(HybridSearchResult.SearchResultItem.ResultSource.VECTOR_ONLY);

        result.setResults(Arrays.asList(item1, item2));
        result.setVectorResultCount(2);
        result.setKeywordResultCount(1);
        result.setTotalTime(100);
        result.setVectorSearchTime(60);
        result.setKeywordSearchTime(30);
        result.setFusionTime(10);

        // 验证结果
        assertEquals(2, result.getResults().size(), "应该有2个结果");
        assertEquals(2, result.getVectorResultCount(), "向量结果数量应该是2");
        assertEquals(1, result.getKeywordResultCount(), "关键词结果数量应该是1");
        assertEquals(100, result.getTotalTime(), "总时间应该是100ms");
        assertEquals(60, result.getVectorSearchTime(), "向量检索时间应该是60ms");
        assertEquals(30, result.getKeywordSearchTime(), "关键词检索时间应该是30ms");
        assertEquals(10, result.getFusionTime(), "融合时间应该是10ms");

        // 验证结果项
        HybridSearchResult.SearchResultItem firstItem = result.getResults().get(0);
        assertEquals("测试内容1", firstItem.getContent());
        assertEquals(0.8, firstItem.getScore(), 0.01);
        assertEquals(0.7, firstItem.getVectorScore(), 0.01);
        assertEquals(0.9, firstItem.getKeywordScore(), 0.01);
        assertEquals(HybridSearchResult.SearchResultItem.ResultSource.HYBRID, firstItem.getSource());
    }

    @Test
    void testDocumentRemoval() {
        logger.info("测试文档删除功能");

        // 添加测试文档
        Map<String, String> metadata = new HashMap<>();
        metadata.put("knowledgeBaseId", "1");
        metadata.put("documentId", "101");
        keywordSearchEngine.addDocument("1_101", "测试文档内容", metadata);

        // 验证文档已添加
        assertEquals(1, keywordSearchEngine.getDocumentCount());

        // 删除文档
        keywordSearchEngine.removeDocument("1_101");

        // 验证文档已删除
        assertEquals(0, keywordSearchEngine.getDocumentCount());

        // 搜索应该没有结果
        List<KeywordSearchEngine.KeywordSearchResult> results = keywordSearchEngine.search("测试", 10, 0.1);
        assertEquals(0, results.size(), "删除后搜索应该没有结果");
    }

    @Test
    void testIndexClear() {
        logger.info("测试索引清空功能");

        // 添加多个测试文档
        for (int i = 1; i <= 5; i++) {
            Map<String, String> metadata = new HashMap<>();
            metadata.put("knowledgeBaseId", "1");
            metadata.put("documentId", String.valueOf(100 + i));
            keywordSearchEngine.addDocument("1_" + (100 + i), "测试文档内容 " + i, metadata);
        }

        // 验证文档已添加
        assertEquals(5, keywordSearchEngine.getDocumentCount());

        // 清空索引
        keywordSearchEngine.clearIndex();

        // 验证索引已清空
        assertEquals(0, keywordSearchEngine.getDocumentCount());

        // 搜索应该没有结果
        List<KeywordSearchEngine.KeywordSearchResult> results = keywordSearchEngine.search("测试", 10, 0.1);
        assertEquals(0, results.size(), "清空后搜索应该没有结果");
    }
}
