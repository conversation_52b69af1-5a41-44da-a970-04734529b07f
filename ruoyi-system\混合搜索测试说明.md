# 混合搜索功能测试说明

## 测试知识库文档

我已为您创建了一个专门的测试文档 `test_knowledge_base.txt`，该文档包含了丰富的AI技术内容，非常适合测试混合搜索功能的差异。

## 测试场景设计

### 1. 精确术语搜索测试
**查询**: "LSTM"
- **纯向量搜索**: 可能找到相关的神经网络内容，但不一定准确定位到LSTM
- **混合搜索**: 能精确找到包含"LSTM"术语的段落，同时结合语义相关性

### 2. 缩写词搜索测试
**查询**: "NLP"
- **纯向量搜索**: 可能无法准确理解缩写词的含义
- **混合搜索**: 能精确匹配"NLP"并找到自然语言处理相关内容

### 3. 专有名词搜索测试
**查询**: "AlphaGo"
- **纯向量搜索**: 可能找到围棋或游戏相关内容，但不够精确
- **混合搜索**: 能精确定位到AlphaGo的具体描述

### 4. 数字和版本搜索测试
**查询**: "GPT-4"
- **纯向量搜索**: 可能找到GPT相关内容，但版本号匹配不准确
- **混合搜索**: 能精确匹配"GPT-4"这个特定版本

### 5. 英文算法名称搜索测试
**查询**: "Random Forest"
- **纯向量搜索**: 可能理解为"随机"和"森林"的语义
- **混合搜索**: 能精确匹配算法名称并找到相关技术描述

## 测试步骤

### 步骤1：创建知识库
1. 使用提供的 `test_knowledge_base.txt` 文件创建新的知识库
2. 等待文档处理和向量化完成

### 步骤2：纯向量搜索测试
使用现有的搜索接口（不启用混合搜索）：
```http
POST /knowledge/search
{
  "knowledgeBaseId": 1,
  "query": "LSTM",
  "maxResults": 5
}
```

### 步骤3：混合搜索测试
使用新的混合搜索接口：
```http
POST /hybrid-search/search
{
  "query": "LSTM",
  "knowledgeBaseId": 1,
  "maxResults": 5,
  "vectorWeight": 0.7,
  "keywordWeight": 0.3,
  "rerankingAlgorithm": "WEIGHTED_FUSION"
}
```

### 步骤4：对比分析
比较两种搜索方式的结果：
- 结果相关性
- 精确匹配程度
- 搜索响应时间
- 结果排序质量

## 预期差异

### 1. 精确匹配能力
- **纯向量搜索**: 依赖语义相似度，可能遗漏精确术语
- **混合搜索**: 结合关键词匹配，确保重要术语不被遗漏

### 2. 专业术语处理
- **纯向量搜索**: 对缩写词、专有名词理解有限
- **混合搜索**: 能精确匹配专业术语和缩写词

### 3. 结果完整性
- **纯向量搜索**: 可能遗漏一些相关但语义距离较远的内容
- **混合搜索**: 通过关键词搜索补充，提供更全面的结果

### 4. 排序准确性
- **纯向量搜索**: 纯粹基于语义相似度排序
- **混合搜索**: 综合考虑语义相似度和关键词匹配度

## 具体测试用例

### 测试用例1：技术缩写词
```
查询: "CNN"
预期差异:
- 纯向量: 可能找到神经网络相关内容，但不够精确
- 混合搜索: 精确定位到"卷积神经网络（CNN）"的描述
```

### 测试用例2：算法名称
```
查询: "K-means"
预期差异:
- 纯向量: 可能理解为聚类相关内容
- 混合搜索: 精确匹配"K均值聚类（K-means Clustering）"
```

### 测试用例3：年份信息
```
查询: "2018年"
预期差异:
- 纯向量: 时间信息语义化程度低
- 混合搜索: 能精确找到包含"2018"的技术发展时间线
```

### 测试用例4：评价指标
```
查询: "F1-Score"
预期差异:
- 纯向量: 可能找到评价相关内容
- 混合搜索: 精确定位到"F1分数（F1-Score）"的定义
```

### 测试用例5：复合查询
```
查询: "深度学习 CNN"
预期差异:
- 纯向量: 主要基于语义相似度
- 混合搜索: 同时匹配"深度学习"和"CNN"，结果更精确
```

## 性能对比测试

使用性能测试接口比较两种搜索方式：
```http
POST /hybrid-search/performance-test
{
  "query": "机器学习算法",
  "knowledgeBaseId": 1,
  "iterations": 100
}
```

关注指标：
- 平均响应时间
- 结果准确率
- 召回率
- 用户满意度

## 总结

通过这个测试知识库，您可以清楚地看到混合搜索相比纯向量搜索的优势：
1. **更高的精确度**: 精确匹配重要术语
2. **更好的召回率**: 不遗漏相关内容
3. **更智能的排序**: 综合多种相关性信号
4. **更强的鲁棒性**: 处理各种类型的查询

建议按照上述测试用例逐一验证，您会发现混合搜索在处理专业术语、缩写词、数字信息等方面有显著优势。
