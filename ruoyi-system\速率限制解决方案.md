# 速率限制问题解决方案

## 问题描述
在上传大文档到知识库时，遇到gRPC速率限制错误：
```
Retry(7) with interval 3000ms. Reason: request is rejected by grpc RateLimiter middleware, please retry later: rate limit exceeded[rate=0.1]
```

## 解决方案

### 1. 立即解决方案（推荐）

#### 方案A：使用内存存储（最简单）
修改 `application.yml` 配置：
```yaml
knowledge:
  embedding:
    store:
      type: memory  # 改为memory，避免gRPC限制
```

**优点**：
- 立即生效，无需重启服务
- 没有网络请求，无速率限制
- 适合测试和小规模使用

**缺点**：
- 重启后数据丢失
- 内存占用较大

#### 方案B：分割文档（推荐用于大文档）
将大文档分割成小文件，分批上传：

1. 将 `test_knowledge_base.txt` 分割成多个小文件（每个文件1-2KB）
2. 分别上传每个小文件
3. 每次上传间隔10-15秒

### 2. 配置优化方案

我已经为您优化了配置，重启服务后生效：

```yaml
knowledge:
  embedding:
    processing:
      chunk-size: 1000        # 增加块大小，减少请求数
      chunk-overlap: 100      # 重叠大小
      batch-size: 5           # 小批次处理
      batch-delay: 2000       # 批次间延迟2秒
      max-retries: 10         # 增加重试次数
      retry-delay: 5000       # 重试延迟5秒
      backoff-multiplier: 1.5 # 指数退避
```

### 3. 服务端解决方案

我已经创建了 `RateLimitedDocumentProcessor` 服务，它会：
- 自动分批处理文档
- 在批次间添加延迟
- 实现指数退避重试机制
- 单个向量化请求间添加100ms延迟

## 立即可用的解决步骤

### 步骤1：修改存储类型（最快解决）
```bash
# 编辑配置文件
vim ruoyi-admin/src/main/resources/application.yml

# 找到这一行：
# type: milvus
# 改为：
# type: memory
```

### 步骤2：重启服务
```bash
# 重启Spring Boot应用
# 或者如果是开发环境，重新运行main方法
```

### 步骤3：重新上传文档
现在可以正常上传 `test_knowledge_base.txt` 文件了。

## 测试验证

上传成功后，可以测试以下查询来验证混合搜索功能：

### 精确术语测试
```
查询: "LSTM"
预期: 找到"长短期记忆网络（LSTM）"相关内容
```

### 缩写词测试
```
查询: "CNN" 
预期: 找到"卷积神经网络（CNN）"相关内容
```

### 算法名称测试
```
查询: "Random Forest"
预期: 找到"随机森林"算法描述
```

### 版本号测试
```
查询: "GPT-4"
预期: 找到GPT-4相关描述
```

## 长期解决方案

### 1. 使用Redis存储
```yaml
knowledge:
  embedding:
    store:
      type: redis
```
需要安装Redis Stack。

### 2. 优化Milvus配置
如果必须使用Milvus，可以：
- 增加连接池大小
- 调整批处理参数
- 使用本地Milvus实例

### 3. 使用外部向量化服务
考虑使用支持更高并发的向量化服务。

## 监控和调试

### 查看处理进度
```java
// 在日志中查看处理进度
logger.info("处理批次 {}/{}, 段落数: {}", i + 1, batchCount, batch.size());
```

### 估算处理时间
```java
// 使用RateLimitedDocumentProcessor估算时间
long estimatedTime = processor.estimateProcessingTime(segmentCount);
```

## 注意事项

1. **内存使用**：使用memory存储时注意内存占用
2. **数据持久化**：memory存储重启后数据丢失
3. **并发处理**：避免同时上传多个大文档
4. **文档大小**：建议单个文档不超过100KB

## 总结

**立即解决**：修改配置使用memory存储
**测试验证**：上传文档并测试混合搜索功能
**长期优化**：根据实际需求选择合适的存储方案

这样您就可以立即体验混合搜索功能的优势了！
