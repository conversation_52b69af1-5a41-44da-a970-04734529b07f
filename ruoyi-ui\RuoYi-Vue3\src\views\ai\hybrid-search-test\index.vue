<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>混合检索测试</span>
          <el-button type="primary" @click="getIndexStats">索引统计</el-button>
        </div>
      </template>

      <!-- 搜索表单 -->
      <el-form :model="searchForm" label-width="120px" class="search-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="知识库ID">
              <el-select v-model="searchForm.knowledgeBaseId" placeholder="请选择知识库" style="width: 100%">
                <el-option
                  v-for="kb in knowledgeBaseList"
                  :key="kb.id"
                  :label="kb.name"
                  :value="kb.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="最大结果数">
              <el-input-number v-model="searchForm.maxResults" :min="1" :max="50" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="向量权重">
              <el-slider v-model="searchForm.vectorWeight" :min="0" :max="1" :step="0.1" show-input />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关键词权重">
              <el-slider v-model="searchForm.keywordWeight" :min="0" :max="1" :step="0.1" show-input />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="启用向量检索">
              <el-switch v-model="searchForm.enableVectorSearch" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="启用关键词检索">
              <el-switch v-model="searchForm.enableKeywordSearch" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="重排序算法">
          <el-radio-group v-model="searchForm.rerankingType">
            <el-radio label="WEIGHTED_FUSION">加权融合</el-radio>
            <el-radio label="RRF">RRF算法</el-radio>
            <el-radio label="LINEAR_COMBINATION">线性组合</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="查询内容">
          <el-input
            v-model="searchForm.query"
            type="textarea"
            :rows="3"
            placeholder="请输入要搜索的内容"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="performHybridSearch" :loading="loading">
            执行混合检索
          </el-button>
          <el-button @click="performanceTest" :loading="performanceLoading">
            性能测试
          </el-button>
          <el-button type="danger" @click="clearIndex">
            清空索引
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 索引统计信息 -->
    <el-card class="box-card" v-if="indexStats">
      <template #header>
        <span>索引统计信息</span>
      </template>
      <el-descriptions :column="3" border>
        <el-descriptions-item label="文档数量">{{ indexStats.documentCount }}</el-descriptions-item>
        <el-descriptions-item label="索引类型">{{ indexStats.indexType }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="indexStats.status === 'active' ? 'success' : 'info'">
            {{ indexStats.status }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 搜索结果 -->
    <el-card class="box-card" v-if="searchResults.length > 0">
      <template #header>
        <div class="card-header">
          <span>搜索结果 ({{ searchResults.length }}条)</span>
          <el-tag type="info">总耗时: {{ lastSearchTime }}ms</el-tag>
        </div>
      </template>

      <div v-for="(result, index) in searchResults" :key="index" class="search-result-item">
        <el-card shadow="hover">
          <div class="result-header">
            <el-tag :type="getSourceTagType(result.source)">{{ getSourceText(result.source) }}</el-tag>
            <span class="result-score">综合分数: {{ result.score.toFixed(3) }}</span>
          </div>
          <div class="result-scores">
            <span v-if="result.vectorScore > 0">向量分数: {{ result.vectorScore.toFixed(3) }}</span>
            <span v-if="result.keywordScore > 0">关键词分数: {{ result.keywordScore.toFixed(3) }}</span>
          </div>
          <div class="result-content">{{ result.content }}</div>
        </el-card>
      </div>
    </el-card>

    <!-- 性能测试结果 -->
    <el-card class="box-card" v-if="performanceResults">
      <template #header>
        <span>性能测试结果</span>
      </template>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="测试次数">{{ performanceResults.iterations }}</el-descriptions-item>
        <el-descriptions-item label="平均耗时">{{ performanceResults.averageTime }}ms</el-descriptions-item>
        <el-descriptions-item label="平均结果数">{{ performanceResults.averageResults }}</el-descriptions-item>
        <el-descriptions-item label="总耗时">{{ performanceResults.totalTime }}ms</el-descriptions-item>
      </el-descriptions>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { listKnowledgeBase } from '@/api/knowledge/base'

// 响应式数据
const loading = ref(false)
const performanceLoading = ref(false)
const knowledgeBaseList = ref([])
const indexStats = ref(null)
const searchResults = ref([])
const lastSearchTime = ref(0)
const performanceResults = ref(null)

// 搜索表单
const searchForm = ref({
  knowledgeBaseId: null,
  query: '',
  maxResults: 10,
  vectorWeight: 0.7,
  keywordWeight: 0.3,
  enableVectorSearch: true,
  enableKeywordSearch: true,
  rerankingType: 'WEIGHTED_FUSION'
})

// 生命周期
onMounted(() => {
  loadKnowledgeBaseList()
  getIndexStats()
})

// 加载知识库列表
const loadKnowledgeBaseList = async () => {
  try {
    const response = await listKnowledgeBase({})
    knowledgeBaseList.value = response.rows || []
  } catch (error) {
    ElMessage.error('加载知识库列表失败')
  }
}

// 获取索引统计信息
const getIndexStats = async () => {
  try {
    const response = await fetch('/dev-api/knowledge/hybrid-search/keyword-index/stats')
    const result = await response.json()
    if (result.code === 200) {
      indexStats.value = result.data
    }
  } catch (error) {
    console.error('获取索引统计失败:', error)
  }
}

// 执行混合检索
const performHybridSearch = async () => {
  if (!searchForm.value.knowledgeBaseId) {
    ElMessage.warning('请选择知识库')
    return
  }
  
  if (!searchForm.value.query.trim()) {
    ElMessage.warning('请输入查询内容')
    return
  }

  loading.value = true
  try {
    const response = await fetch('/dev-api/knowledge/hybrid-search/search', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(searchForm.value)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      searchResults.value = result.data.results || []
      lastSearchTime.value = result.data.totalTime || 0
      ElMessage.success(`搜索完成，找到 ${searchResults.value.length} 条结果`)
    } else {
      ElMessage.error(result.msg || '搜索失败')
    }
  } catch (error) {
    ElMessage.error('搜索请求失败')
  } finally {
    loading.value = false
  }
}

// 性能测试
const performanceTest = async () => {
  if (!searchForm.value.knowledgeBaseId || !searchForm.value.query.trim()) {
    ElMessage.warning('请先设置知识库和查询内容')
    return
  }

  performanceLoading.value = true
  try {
    const testParams = {
      knowledgeBaseId: searchForm.value.knowledgeBaseId,
      query: searchForm.value.query,
      iterations: 10
    }

    const response = await fetch('/dev-api/knowledge/hybrid-search/performance-test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testParams)
    })
    
    const result = await response.json()
    if (result.code === 200) {
      performanceResults.value = result.data
      ElMessage.success('性能测试完成')
    } else {
      ElMessage.error(result.msg || '性能测试失败')
    }
  } catch (error) {
    ElMessage.error('性能测试请求失败')
  } finally {
    performanceLoading.value = false
  }
}

// 清空索引
const clearIndex = async () => {
  try {
    await ElMessageBox.confirm('确定要清空关键词索引吗？此操作不可恢复。', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const response = await fetch('/dev-api/knowledge/hybrid-search/keyword-index/clear', {
      method: 'DELETE'
    })
    
    const result = await response.json()
    if (result.code === 200) {
      ElMessage.success('索引已清空')
      getIndexStats()
    } else {
      ElMessage.error(result.msg || '清空索引失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空索引请求失败')
    }
  }
}

// 获取来源标签类型
const getSourceTagType = (source) => {
  switch (source) {
    case 'HYBRID': return 'success'
    case 'VECTOR_ONLY': return 'primary'
    case 'KEYWORD_ONLY': return 'warning'
    default: return 'info'
  }
}

// 获取来源文本
const getSourceText = (source) => {
  switch (source) {
    case 'HYBRID': return '混合检索'
    case 'VECTOR_ONLY': return '向量检索'
    case 'KEYWORD_ONLY': return '关键词检索'
    default: return '未知'
  }
}
</script>

<style scoped>
.search-form {
  margin-bottom: 20px;
}

.search-result-item {
  margin-bottom: 15px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-score {
  font-weight: bold;
  color: #409eff;
}

.result-scores {
  margin-bottom: 10px;
  font-size: 12px;
  color: #909399;
}

.result-scores span {
  margin-right: 15px;
}

.result-content {
  line-height: 1.6;
  color: #303133;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  margin-bottom: 20px;
}
</style>
